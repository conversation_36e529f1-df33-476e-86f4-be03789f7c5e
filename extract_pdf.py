#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import PyPDF2
import sys
import os

def extract_pdf_text(pdf_path):
    """
    Extrai texto de um arquivo PDF
    """
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += f"\n--- PÁGINA {page_num + 1} ---\n"
                text += page.extract_text()
                text += "\n"
            
            return text
    except Exception as e:
        return f"Erro ao extrair texto do PDF: {str(e)}"

if __name__ == "__main__":
    pdf_file = "Resumo Executivo da Proposta de Dissertação.pdf"
    
    if os.path.exists(pdf_file):
        extracted_text = extract_pdf_text(pdf_file)
        print(extracted_text)
    else:
        print(f"Arquivo não encontrado: {pdf_file}")
