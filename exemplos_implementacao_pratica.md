# Exemplos Práticos de Implementação da Metodologia

## 1. Exemplo de Extração de Questão e Resposta

### Caso Real Simulado: Tese sobre Diabetes

**Trecho Original da Tese:**
```
"O objetivo principal desta pesquisa é investigar como a implementação de um programa 
de educação nutricional impacta no controle glicêmico de pacientes diabéticos tipo 2 
em unidades básicas de saúde."

[...]

"Os resultados demonstraram que pacientes que participaram do programa de educação 
nutricional apresentaram redução média de 1,2% na hemoglobina glicada (HbA1c) após 
6 meses de intervenção (p<0,001), comparado ao grupo controle que manteve níveis 
estáveis. Adicionalmente, observou-se melhoria significativa nos hábitos alimentares, 
com aumento de 40% no consumo de fibras e redução de 25% na ingestão de açúcares 
simples."
```

**Questão Extraída:**
```json
{
  "questao_id": "Q001",
  "questao_original": "Como a implementação de um programa de educação nutricional impacta no controle glicêmico de pacientes diabéticos tipo 2?",
  "tipo_questao": "analitica",
  "complexidade": "media"
}
```

**Resposta Original Extraída:**
```json
{
  "resposta_original": "A implementação do programa de educação nutricional resultou em redução média de 1,2% na hemoglobina glicada (HbA1c) após 6 meses, com significância estatística (p<0,001). Houve também melhoria nos hábitos alimentares: aumento de 40% no consumo de fibras e redução de 25% na ingestão de açúcares simples.",
  "evidencias": ["HbA1c redução 1,2%", "p<0,001", "fibras +40%", "açúcares -25%"],
  "tipo_evidencia": "quantitativa"
}
```

## 2. Exemplo de Resposta Gerada pela IA

### Input para o Sistema de IA:
```
Contexto: [Documentos relevantes sobre educação nutricional e diabetes do corpus]

Questão: Como a implementação de um programa de educação nutricional impacta no controle glicêmico de pacientes diabéticos tipo 2?

Responda baseando-se exclusivamente no contexto fornecido.
```

### Output da IA:
```
"Com base nos estudos analisados, a implementação de programas de educação nutricional 
demonstra impacto positivo significativo no controle glicêmico de pacientes diabéticos 
tipo 2. Os resultados indicam redução média na hemoglobina glicada entre 1,0% a 1,5% 
após intervenções de 6 meses. Observa-se também melhoria nos padrões alimentares, 
incluindo aumento no consumo de fibras e redução na ingestão de carboidratos simples. 
A efetividade está associada à frequência das sessões educativas e ao envolvimento 
multidisciplinar da equipe de saúde."
```

## 3. Avaliação Comparativa

### Análise Automática (BERT Score):
```python
from sentence_transformers import SentenceTransformer
import numpy as np

model = SentenceTransformer('all-MiniLM-L6-v2')

resposta_original = "A implementação do programa de educação nutricional resultou em redução média de 1,2% na hemoglobina glicada..."

resposta_ia = "Com base nos estudos analisados, a implementação de programas de educação nutricional demonstra impacto positivo..."

# Calcular embeddings
emb_original = model.encode([resposta_original])
emb_ia = model.encode([resposta_ia])

# Calcular similaridade
similaridade = np.dot(emb_original, emb_ia.T)[0][0]
print(f"Similaridade semântica: {similaridade:.3f}")
# Output esperado: ~0.85
```

### Avaliação por Especialista:
```
AVALIAÇÃO QUALITATIVA:

1. FIDEDIGNIDADE: 4/5
   - A IA capturou adequadamente o impacto positivo
   - Valores numéricos aproximados (1,0-1,5% vs 1,2% original)
   
2. COMPLETUDE: 3/5
   - Cobriu aspectos principais mas omitiu dados específicos
   - Não mencionou significância estatística (p<0,001)
   
3. PRECISÃO: 3/5
   - Informações corretas mas generalizadas
   - Perdeu especificidade dos dados originais
   
4. CLAREZA: 5/5
   - Resposta bem estruturada e compreensível
   
5. UTILIDADE ACADÊMICA: 4/5
   - Útil para visão geral, limitada para análise detalhada

SCORE FINAL: 3.8/5
```

## 4. Exemplo de Análise Temporal

### Comparação entre Períodos:

**Questão Tipo: "Qual a prevalência de hipertensão em idosos?"**

**Período 2005-2015:**
```json
{
  "respostas_originais": [
    "A prevalência de hipertensão em idosos varia entre 60-70% segundo estudos nacionais",
    "Dados do VIGITEL indicam prevalência de 65% em maiores de 65 anos"
  ],
  "caracteristicas": {
    "precisao_numerica": "moderada",
    "fontes_citadas": ["VIGITEL", "estudos nacionais"],
    "metodologia": "surveys populacionais"
  }
}
```

**Período 2015-2025:**
```json
{
  "respostas_originais": [
    "Meta-análise de 15 estudos brasileiros indica prevalência de 68,2% (IC95%: 65,1-71,3%) em idosos ≥65 anos",
    "Análise de dados do ELSI-Brasil demonstra prevalência de 71,4% com variação regional significativa (p<0,001)"
  ],
  "caracteristicas": {
    "precisao_numerica": "alta",
    "fontes_citadas": ["meta-análise", "ELSI-Brasil", "15 estudos"],
    "metodologia": "meta-análise + coorte nacional"
  }
}
```

**Análise da Evolução:**
- **Precisão:** Aumento na especificidade estatística
- **Metodologia:** Evolução para abordagens mais robustas
- **Complexidade:** Maior sofisticação analítica no período recente

## 5. Código de Implementação do Sistema

### Pipeline Básico de RAG:

```python
import chromadb
from sentence_transformers import SentenceTransformer
from transformers import pipeline
import json

class SistemaValidacaoIA:
    def __init__(self):
        self.client = chromadb.Client()
        self.collection = self.client.create_collection("teses_corpus")
        self.encoder = SentenceTransformer('all-MiniLM-L6-v2')
        self.generator = pipeline("text-generation", 
                                model="microsoft/DialoGPT-medium")
    
    def adicionar_documento(self, texto, metadados):
        """Adiciona documento ao corpus vetorizado"""
        embedding = self.encoder.encode([texto])
        self.collection.add(
            embeddings=embedding.tolist(),
            documents=[texto],
            metadatas=[metadados],
            ids=[metadados['id']]
        )
    
    def buscar_contexto(self, questao, n_resultados=5):
        """Busca documentos relevantes para a questão"""
        query_embedding = self.encoder.encode([questao])
        resultados = self.collection.query(
            query_embeddings=query_embedding.tolist(),
            n_results=n_resultados
        )
        return resultados['documents'][0]
    
    def gerar_resposta(self, questao):
        """Gera resposta baseada no contexto recuperado"""
        contexto = self.buscar_contexto(questao)
        
        prompt = f"""
        Contexto: {' '.join(contexto)}
        
        Questão: {questao}
        
        Com base exclusivamente no contexto fornecido, responda:
        """
        
        resposta = self.generator(prompt, max_length=200, 
                                num_return_sequences=1)
        return resposta[0]['generated_text']
    
    def avaliar_resposta(self, questao, resposta_ia, resposta_original):
        """Avalia qualidade da resposta gerada"""
        # BERT Score
        emb_ia = self.encoder.encode([resposta_ia])
        emb_original = self.encoder.encode([resposta_original])
        bert_score = np.dot(emb_ia, emb_original.T)[0][0]
        
        # Cobertura de conceitos (simplificado)
        palavras_original = set(resposta_original.lower().split())
        palavras_ia = set(resposta_ia.lower().split())
        cobertura = len(palavras_original & palavras_ia) / len(palavras_original)
        
        return {
            "bert_score": float(bert_score),
            "cobertura_conceitos": float(cobertura),
            "score_composto": (bert_score * 0.7 + cobertura * 0.3)
        }

# Exemplo de uso
sistema = SistemaValidacaoIA()

# Adicionar documentos ao corpus
documento = {
    "id": "T001",
    "texto": "Texto completo da tese...",
    "metadados": {"ano": 2020, "area": "saude", "tipo": "mestrado"}
}
sistema.adicionar_documento(documento["texto"], documento["metadados"])

# Testar questão
questao = "Como a educação nutricional impacta o controle glicêmico?"
resposta_gerada = sistema.gerar_resposta(questao)
resposta_original = "A educação nutricional reduz HbA1c em 1,2%..."

# Avaliar resultado
avaliacao = sistema.avaliar_resposta(questao, resposta_gerada, resposta_original)
print(f"Score composto: {avaliacao['score_composto']:.3f}")
```

## 6. Exemplo de Análise Estatística

### Comparação entre Tipos de Questão:

```python
import pandas as pd
import scipy.stats as stats
import matplotlib.pyplot as plt

# Dados simulados de avaliação
dados = {
    'tipo_questao': ['factual']*50 + ['analitica']*50 + ['avaliativa']*50,
    'bert_score': [0.85, 0.82, 0.88, ...],  # 150 valores
    'avaliacao_especialista': [4.2, 3.8, 4.5, ...],  # 150 valores
    'periodo': ['2005-2015']*75 + ['2015-2025']*75
}

df = pd.DataFrame(dados)

# ANOVA para comparar tipos de questão
f_stat, p_value = stats.f_oneway(
    df[df['tipo_questao']=='factual']['bert_score'],
    df[df['tipo_questao']=='analitica']['bert_score'],
    df[df['tipo_questao']=='avaliativa']['bert_score']
)

print(f"ANOVA - F: {f_stat:.3f}, p: {p_value:.3f}")

# Teste t para comparação temporal
t_stat, p_value = stats.ttest_ind(
    df[df['periodo']=='2005-2015']['bert_score'],
    df[df['periodo']=='2015-2025']['bert_score']
)

print(f"Teste t temporal - t: {t_stat:.3f}, p: {p_value:.3f}")

# Visualização
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
df.boxplot(column='bert_score', by='tipo_questao', ax=plt.gca())
plt.title('BERT Score por Tipo de Questão')

plt.subplot(1, 2, 2)
df.boxplot(column='bert_score', by='periodo', ax=plt.gca())
plt.title('BERT Score por Período')

plt.tight_layout()
plt.show()
```

## 7. Protocolo de Validação Cruzada

### Exemplo de Implementação:

```python
from sklearn.model_selection import KFold
import numpy as np

def validacao_cruzada(questoes, respostas_originais, sistema_ia, k=5):
    """
    Implementa validação cruzada k-fold para o sistema de IA
    """
    kf = KFold(n_splits=k, shuffle=True, random_state=42)
    scores = []
    
    for fold, (train_idx, test_idx) in enumerate(kf.split(questoes)):
        print(f"Fold {fold + 1}/{k}")
        
        # Treinar/configurar sistema com dados de treino
        questoes_treino = [questoes[i] for i in train_idx]
        respostas_treino = [respostas_originais[i] for i in train_idx]
        
        # Testar com dados de teste
        questoes_teste = [questoes[i] for i in test_idx]
        respostas_teste = [respostas_originais[i] for i in test_idx]
        
        scores_fold = []
        for q, r_original in zip(questoes_teste, respostas_teste):
            r_gerada = sistema_ia.gerar_resposta(q)
            score = sistema_ia.avaliar_resposta(q, r_gerada, r_original)
            scores_fold.append(score['score_composto'])
        
        scores.append(np.mean(scores_fold))
        print(f"Score médio do fold: {np.mean(scores_fold):.3f}")
    
    print(f"\nScore médio geral: {np.mean(scores):.3f} ± {np.std(scores):.3f}")
    return scores

# Exemplo de uso
questoes = ["Como...", "Por que...", "Qual..."]  # Lista de questões
respostas = ["Resposta 1", "Resposta 2", "Resposta 3"]  # Respostas originais

scores_cv = validacao_cruzada(questoes, respostas, sistema)
```

## 8. Relatório de Resultados Exemplo

### Síntese de Achados:

```markdown
## RESULTADOS DA VALIDAÇÃO

### Métricas Gerais (n=200)
- **BERT Score Médio:** 0.78 ± 0.12
- **Avaliação Especialistas:** 3.6 ± 0.8 (escala 1-5)
- **Cobertura de Conceitos:** 0.72 ± 0.15

### Por Tipo de Questão
| Tipo | BERT Score | Avaliação | Cobertura |
|------|------------|-----------|-----------|
| Factual | 0.85 ± 0.08 | 4.1 ± 0.6 | 0.81 ± 0.10 |
| Analítica | 0.76 ± 0.11 | 3.4 ± 0.7 | 0.68 ± 0.14 |
| Avaliativa | 0.71 ± 0.14 | 3.2 ± 0.9 | 0.65 ± 0.18 |

### Análise Temporal
- **2005-2015:** Score médio 0.75 ± 0.13
- **2015-2025:** Score médio 0.81 ± 0.11
- **Diferença significativa:** p < 0.001

### Principais Limitações Identificadas
1. Dificuldade com interpretações complexas
2. Perda de especificidade numérica
3. Generalização excessiva de conclusões
```

Este documento fornece exemplos concretos de como implementar cada aspecto da metodologia proposta, desde a extração de dados até a análise final dos resultados.
