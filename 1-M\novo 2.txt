--- PÁGINA 1 ---
Resumo Executivo da Proposta de Dissertação
Título
"Validação de Respostas de Inteligência Artificial Generativa Baseada em Corpus de Teses e
Dissertações: Um Estudo sobre Recuperação e Representação do Conhecimento Científico"
Síntese da Proposta
O QUE? - Objeto de Pesquisa
Desenvolver e validar uma metodologia para verificar se sistemas de IA generativa conseguem
reproduzir adequadamente o conhecimento contido em teses e dissertações quando alimentados
com corpus científico especializado.
POR QUE? - Justificativa
Com o crescimento exponencial do uso de IA generativa para consulta científica, é fundamental
validar se essas tecnologias realmente representam o conhecimento acadêmico de forma
fidedigna, especialmente considerando os riscos dos dados sintéticos discutidos na literatura.
COMO? - Metodologia Central
1. Constituir corpus de 200 teses/dissertações da área de saúde (100 de 2005-2015, 100 de
2015-2025)
2. Extrair questões de pesquisa e suas respostas dos trabalhos
3. Implementar sistema de IA generativa alimentado pelo corpus
4. Comparar respostas da IA com respostas originais das teses
5. Validar através de métricas quantitativas e qualitativas

--- PÁGINA 2 ---
ONDE? - Fonte dos Dados
BDTD (Biblioteca Digital Brasileira de Teses e Dissertações)
Área de aplicação: Ciências da Saúde (contexto, não foco)
Período: Análise temporal comparativa entre duas décadas
Objetivos Principais
Objetivo Geral
Desenvolver metodologia para validar capacidade de sistemas de IA generativa em reproduzir
respostas contidas em teses e dissertações.
Objetivos Específicos (Resumidos)
1. Constituir corpus representativo de teses da área de saúde
2. Extrair questões e respostas dos trabalhos selecionados
3. Implementar sistema de IA generativa com o corpus
4. Desenvolver métricas de validação para comparar respostas
5. Analisar evolução temporal das respostas (2005-2015 vs 2015-2025)
6. Propor diretrizes para implementação em repositórios
Contribuições Esperadas
Para a Ciência da Informação
Metodologia replicável para validação de sistemas de IA em repositórios
Avanços na recuperação da informação científica
Reflexões sobre mediação da informação por IA

--- PÁGINA 3 ---
Diretrizes para gestão de repositórios digitais
Para a Área Tecnológica
Sistema funcional de consulta baseado em IA generativa
Métricas de avaliação para sistemas similares
Análise de limitações dos modelos atuais
Prototipo aplicável a outros domínios
Para a Comunidade Científica
Ferramenta de validação de sistemas de IA
Reflexões epistemológicas sobre dados sintéticos
Análise da evolução do conhecimento científico
Alertas sobre riscos da recursividade informacional
Metodologia Resumida
Tipo de Pesquisa
Natureza: Aplicada
Abordagem: Quali-quantitativa
Método: Experimental
Etapas Principais
1. Revisão de literatura (2 meses)
2. Constituição do corpus (3 meses)
3. Desenvolvimento do sistema (4 meses)
4. Execução dos experimentos (3 meses)

--- PÁGINA 4 ---
5. Análise dos resultados (4 meses)
6. Redação da dissertação (2 meses)
Validação
Métricas automáticas: BERT Score, similaridade semântica
Avaliação qualitativa: Análise de conteúdo das respostas
Validação por especialistas: Amostra representativa
Análise temporal: Comparação entre períodos
Diferenciais da Proposta
Metodológico
Foco na metodologia, não apenas nos resultados (adequado para mestrado)
Reproducível e transferível para outras áreas
Validação rigorosa com múltiplas métricas
Análise temporal inovadora
Teórico
Interdisciplinar: CI + Computação + domínio de aplicação
Questões epistemológicas sobre dados sintéticos
Reflexões sobre evolução do conhecimento científico
Contribuições para teoria da recuperação da informação
Prático
Aplicabilidade imediata em repositórios

--- PÁGINA 5 ---
Ferramenta útil para pesquisadores
Diretrizes práticas para gestores
Sistema demonstrativo funcional
Cronograma (18 meses)
 Período Atividades Principais
Meses 1-6 Revisão literatura + Constituição corpus + Arquitetura sistema
Meses 7-12 Implementação + Desenvolvimento métricas + Testes iniciais
Meses 13-18 Experimentos + Análise resultados + Redação dissertação
Recursos Necessários
Tecnológicos
Servidor com GPU para processamento
APIs de modelos de linguagem
Ferramentas de desenvolvimento Python/ML
Banco de dados vetorial
Humanos
Orientador especialista em CI
Consultor técnico em IA/ML
Especialistas para validação (voluntários)
Viabilidade

--- PÁGINA 6 ---
Pontos Fortes
✅ Dados disponíveis no BDTD
✅ Ferramentas acessíveis de IA
✅ Metodologia bem definida
✅ Prazo adequado para mestrado
✅ Orientação especializada disponível
Riscos Controlados
⚠ Qualidade variável dos textos → Critérios rigorosos de seleção
⚠ Limitações técnicas da IA → Análise crítica das limitações
⚠ Validação manual trabalhosa → Amostragem representativa
Impacto Esperado
Acadêmico
Publicações em periódicos de CI e áreas afins
Apresentações em congressos nacionais e internacionais
Contribuição metodológica replicável
Base para pesquisas futuras (doutorado)
Prático
Implementação em repositórios institucionais
Ferramenta para bibliotecas digitais
Assistente de pesquisa para acadêmicos
Modelo para outras instituições

--- PÁGINA 7 ---
Alinhamento com a Discussão Original
Esta proposta atende diretamente às orientações do professor:
✅ Foco na metodologia reprodutível (essência do mestrado)
✅ Não se apega ao objeto específico (saúde é contexto, não foco)
✅ Trabalha com dados acessíveis (BDTD ao invés de dados clínicos)
✅ Valida perguntas e respostas (exatamente como sugerido)
✅ Prazo viável para conclusão em 18 meses
✅ Contribuição real para a área de Ciência da Informação
A proposta transforma os desafios identificados na reunião em uma oportunidade de
pesquisa metodologicamente robusta e academicamente contributiva.