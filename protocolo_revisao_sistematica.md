# Protocolo de Revisão Sistemática de Literatura

## Tí<PERSON>lo da Revisão
"Validação de Sistemas de Inteligência Artificial Generativa para Recuperação e Representação de Conhecimento Científico: Uma Revisão Sistemática"

## 1. Questões de Pesquisa

### Questão Principal
Como tem sido abordada a validação de sistemas de IA generativa para recuperação e representação de conhecimento científico na literatura acadêmica?

### Questões Secundárias
1. Quais metodologias têm sido utilizadas para avaliar a qualidade de respostas geradas por IA em contextos acadêmicos?
2. Que métricas de validação são empregadas para comparar conhecimento original vs. conhecimento reproduzido por IA?
3. <PERSON><PERSON><PERSON> s<PERSON> as principais limitações identificadas em sistemas de IA generativa para recuperação de informação científica?
4. Como a literatura aborda questões epistemológicas relacionadas à representação do conhecimento por IA?
5. Que evidências existem sobre a evolução temporal da capacidade de IA em reproduzir conhecimento científico?

## 2. Estratégia de Busca

### 2.1 Bases de Dados Selecionadas

**Bases Principais:**
- Web of Science (Core Collection)
- Scopus
- IEEE Xplore
- ACM Digital Library
- LISA (Library and Information Science Abstracts)

**Bases Complementares:**
- arXiv (pré-prints)
- Google Scholar (literatura cinzenta)
- BRAPCI (literatura nacional em CI)
- SciELO (literatura latino-americana)

### 2.2 Strings de Busca

#### String Principal (Inglês):
```
("artificial intelligence" OR "AI" OR "machine learning" OR "deep learning" OR 
"large language model*" OR "LLM*" OR "generative AI" OR "generative artificial intelligence" OR
"natural language generation" OR "NLG" OR "text generation" OR "GPT" OR "BERT" OR "transformer*")

AND

("knowledge representation" OR "knowledge retrieval" OR "information retrieval" OR 
"document retrieval" OR "scientific knowledge" OR "academic knowledge" OR 
"scholarly information" OR "research knowledge" OR "scientific literature" OR
"academic literature" OR "thesis" OR "dissertation*" OR "scientific document*")

AND

("validation" OR "evaluation" OR "assessment" OR "quality measure*" OR "performance measure*" OR
"accuracy" OR "effectiveness" OR "reliability" OR "faithfulness" OR "fidelity" OR
"reproducibility" OR "replicability" OR "ground truth" OR "human evaluation" OR
"expert evaluation" OR "benchmark*")
```

#### String Secundária (Português):
```
("inteligência artificial" OR "IA" OR "aprendizado de máquina" OR "aprendizagem automática" OR
"modelo de linguagem" OR "IA generativa" OR "geração de texto" OR "processamento de linguagem natural")

AND

("representação do conhecimento" OR "recuperação da informação" OR "recuperação de conhecimento" OR
"conhecimento científico" OR "literatura científica" OR "tese*" OR "dissertaç*" OR
"documento* científico*" OR "informação acadêmica")

AND

("validação" OR "avaliação" OR "medida* de qualidade" OR "eficácia" OR "confiabilidade" OR
"fidedignidade" OR "reprodutibilidade" OR "avaliação humana" OR "avaliação por especialista*")
```

### 2.3 Filtros Temporais e de Idioma
- **Período:** 2018-2024 (6 anos - era dos transformers)
- **Idiomas:** Inglês, Português, Espanhol
- **Tipos de documento:** Artigos, conferências, teses, dissertações

## 3. Critérios de Seleção

### 3.1 Critérios de Inclusão

**Critérios Primários:**
- Estudos que abordam validação/avaliação de sistemas de IA para recuperação de informação
- Pesquisas sobre qualidade de respostas geradas por IA em contextos acadêmicos/científicos
- Trabalhos que comparam conhecimento original vs. reproduzido por IA
- Estudos sobre métricas de avaliação para sistemas de IA generativa

**Critérios Secundários:**
- Pesquisas sobre RAG (Retrieval-Augmented Generation) em domínios científicos
- Estudos sobre representação do conhecimento por modelos de linguagem
- Trabalhos sobre epistemologia da IA em contextos informacionais
- Pesquisas sobre evolução temporal de capacidades de IA

**Critérios de Contexto:**
- Estudos em bibliotecas digitais, repositórios acadêmicos
- Pesquisas em Ciência da Informação, Ciência da Computação, Epistemologia
- Trabalhos sobre sistemas de pergunta-resposta acadêmicos

### 3.2 Critérios de Exclusão

**Exclusões Técnicas:**
- Estudos puramente técnicos sem componente de validação
- Trabalhos sobre IA que não abordam conhecimento científico/acadêmico
- Pesquisas sobre geração de texto criativo/literário
- Estudos sobre chatbots comerciais sem foco acadêmico

**Exclusões Metodológicas:**
- Artigos sem metodologia clara de avaliação
- Trabalhos sem dados empíricos ou validação
- Estudos de caso únicos sem generalização
- Revisões narrativas sem síntese sistemática

**Exclusões de Escopo:**
- Pesquisas sobre IA em diagnóstico médico (fora do escopo informacional)
- Estudos sobre IA para análise de imagens/vídeos
- Trabalhos sobre robótica ou IA física
- Pesquisas sobre aspectos legais/regulatórios apenas

## 4. Processo de Seleção

### 4.1 Fluxo de Seleção (PRISMA)

**Etapa 1: Busca Inicial**
- Execução das strings em todas as bases
- Remoção de duplicatas
- Exportação para gerenciador de referências (Zotero/Mendeley)

**Etapa 2: Triagem por Título e Resumo**
- Dois revisores independentes
- Critérios de inclusão/exclusão aplicados
- Resolução de discordâncias por consenso
- Kappa de Cohen > 0.7 para concordância

**Etapa 3: Leitura Completa**
- Avaliação do texto completo
- Aplicação rigorosa dos critérios
- Extração de dados estruturada
- Avaliação de qualidade metodológica

**Etapa 4: Busca Complementar**
- Análise das referências dos artigos incluídos (snowballing)
- Busca por citações dos artigos-chave (forward citation)
- Consulta a especialistas da área

### 4.2 Extração de Dados

**Dados Bibliométricos:**
- Autor(es), ano, título, periódico/evento
- País, instituição, área de conhecimento
- Tipo de estudo, metodologia empregada

**Dados Substantivos:**
- Objetivo do estudo
- Tipo de sistema de IA avaliado
- Domínio de aplicação (área do conhecimento)
- Metodologia de validação utilizada
- Métricas de avaliação empregadas
- Principais resultados e limitações
- Conclusões e recomendações

**Dados Metodológicos:**
- Tamanho da amostra/corpus
- Tipo de validação (automática, humana, mista)
- Critérios de qualidade utilizados
- Limitações metodológicas reportadas

## 5. Avaliação da Qualidade

### 5.1 Critérios de Qualidade Metodológica

**Para Estudos Experimentais:**
- Clareza dos objetivos e hipóteses
- Adequação do design experimental
- Tamanho e representatividade da amostra
- Controle de variáveis confundidoras
- Validade das métricas utilizadas
- Transparência na reportagem dos resultados

**Para Estudos Observacionais:**
- Clareza dos critérios de seleção
- Representatividade da amostra
- Adequação dos métodos de coleta
- Controle de vieses
- Validade das medidas utilizadas

**Escala de Qualidade (1-5):**
- 5: Excelente qualidade metodológica
- 4: Boa qualidade com pequenas limitações
- 3: Qualidade moderada com algumas limitações
- 2: Qualidade limitada com problemas significativos
- 1: Qualidade inadequada

### 5.2 Critérios de Exclusão por Qualidade
- Estudos com score < 3 serão excluídos da síntese
- Estudos com score 3 serão incluídos com ressalvas
- Análise de sensibilidade considerando apenas estudos score ≥ 4

## 6. Síntese e Análise

### 6.1 Análise Quantitativa (Meta-análise)

**Quando Aplicável:**
- Estudos com métricas comparáveis (ex: accuracy, F1-score)
- Amostras e metodologias similares
- Dados suficientes para cálculo de effect sizes

**Métricas de Interesse:**
- Accuracy média de sistemas de IA
- Correlação entre avaliação automática vs. humana
- Variação de performance por domínio/área

### 6.2 Análise Qualitativa (Síntese Temática)

**Categorias de Análise:**
1. **Metodologias de Validação**
   - Tipos de validação empregados
   - Métricas mais utilizadas
   - Protocolos de avaliação

2. **Limitações Identificadas**
   - Limitações técnicas dos sistemas
   - Limitações metodológicas dos estudos
   - Gaps de conhecimento

3. **Evolução Temporal**
   - Mudanças nas abordagens ao longo do tempo
   - Evolução das capacidades de IA
   - Tendências emergentes

4. **Questões Epistemológicas**
   - Discussões sobre representação do conhecimento
   - Questões de fidedignidade e confiabilidade
   - Implicações para a Ciência da Informação

## 7. Cronograma da Revisão Sistemática

### Mês 1: Planejamento e Busca
- **Semana 1:** Refinamento do protocolo
- **Semana 2:** Execução das buscas
- **Semana 3:** Remoção de duplicatas e organização
- **Semana 4:** Início da triagem por título/resumo

### Mês 2: Seleção e Avaliação
- **Semana 1-2:** Triagem por título/resumo
- **Semana 3-4:** Leitura completa dos textos selecionados

### Mês 3: Extração e Síntese
- **Semana 1-2:** Extração de dados estruturada
- **Semana 3:** Avaliação de qualidade
- **Semana 4:** Síntese e análise dos resultados

## 8. Resultados Esperados

### 8.1 Produtos da Revisão
- **Artigo de revisão sistemática** para submissão em periódico Qualis A
- **Capítulo de fundamentação teórica** da dissertação
- **Mapeamento do estado da arte** na área
- **Identificação de gaps** para justificar a pesquisa

### 8.2 Contribuições Esperadas
- **Panorama abrangente** das metodologias de validação existentes
- **Identificação de métricas** mais adequadas para o contexto
- **Lacunas metodológicas** que sua pesquisa pode preencher
- **Posicionamento teórico** da sua proposta no campo

## 9. Limitações Antecipadas

### 9.1 Limitações da Busca
- Possível viés de publicação (estudos com resultados positivos)
- Literatura cinzenta pode estar sub-representada
- Evolução rápida da área pode tornar alguns estudos obsoletos

### 9.2 Limitações da Síntese
- Heterogeneidade metodológica pode dificultar meta-análise
- Diferentes definições de "validação" entre estudos
- Variação na qualidade dos estudos incluídos

## 10. Considerações Éticas

- **Transparência:** Protocolo registrado e disponibilizado
- **Reprodutibilidade:** Dados de extração disponibilizados
- **Viés:** Dois revisores independentes para minimizar viés
- **Conflitos de interesse:** Declaração de ausência de conflitos

---

**Estimativa de Trabalho:**
- **Artigos iniciais esperados:** 500-800
- **Artigos após triagem:** 50-100
- **Artigos incluídos na síntese:** 20-40
- **Tempo total estimado:** 3 meses (conforme cronograma da dissertação)
